/**
 * Model Master - Controller Layer
 * MVC Architecture - Controller and View Logic
 * No hardcoded values - all configuration from Model.json
 */

class ProductController {
    constructor(modelMaster, dataService) {
        this.modelMaster = modelMaster;
        this.dataService = dataService;
        this.currentPage = 1;
        this.currentFilters = {};
        this.currentSearchQuery = '';
        this.isLoading = false;
        this.init();
    }

    /**
     * Initialize the controller
     */
    init() {
        this.bindEvents();
        this.loadProducts();
        this.loadCategories();
        this.setupFormValidation();
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.handleSearch(e.target.value);
                }, 300);
            });
        }

        // Filter functionality
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.handleCategoryFilter(e.target.value);
            });
        }

        const priceMinFilter = document.getElementById('priceMinFilter');
        const priceMaxFilter = document.getElementById('priceMaxFilter');
        if (priceMinFilter && priceMaxFilter) {
            [priceMinFilter, priceMaxFilter].forEach(input => {
                input.addEventListener('input', () => {
                    this.handlePriceFilter(priceMinFilter.value, priceMaxFilter.value);
                });
            });
        }

        const inStockFilter = document.getElementById('inStockFilter');
        if (inStockFilter) {
            inStockFilter.addEventListener('change', (e) => {
                this.handleStockFilter(e.target.checked);
            });
        }

        // Sort functionality
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.handleSort(e.target.value);
            });
        }

        // Add product form
        const addProductForm = document.getElementById('addProductForm');
        if (addProductForm) {
            addProductForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddProduct(new FormData(addProductForm));
            });
        }

        // Edit product form
        const editProductForm = document.getElementById('editProductForm');
        if (editProductForm) {
            editProductForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleEditProduct(new FormData(editProductForm));
            });
        }

        // Modal events
        const addProductModal = document.getElementById('addProductModal');
        if (addProductModal) {
            addProductModal.addEventListener('show.bs.modal', () => {
                this.resetAddProductForm();
            });
        }

        // Clear filters button
        const clearFiltersBtn = document.getElementById('clearFiltersBtn');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }

        // View toggle buttons
        const gridViewBtn = document.getElementById('gridViewBtn');
        const listViewBtn = document.getElementById('listViewBtn');
        if (gridViewBtn && listViewBtn) {
            gridViewBtn.addEventListener('click', () => this.setView('grid'));
            listViewBtn.addEventListener('click', () => this.setView('list'));
        }
    }

    /**
     * Load and display products
     */
    async loadProducts() {
        try {
            this.setLoading(true);

            const config = this.modelMaster.config;
            const itemsPerPage = config?.config?.itemsPerPage || 12;

            // Get filtered and searched products
            let products = this.dataService.searchProducts(this.currentSearchQuery, this.currentFilters);

            // Apply sorting
            products = this.sortProducts(products);

            // Apply pagination
            const totalItems = products.length;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const startIndex = (this.currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const paginatedProducts = products.slice(startIndex, endIndex);

            // Render products
            this.renderProducts(paginatedProducts);

            // Render pagination
            this.renderPagination({
                currentPage: this.currentPage,
                totalPages: totalPages,
                totalItems: totalItems,
                itemsPerPage: itemsPerPage,
                hasNext: this.currentPage < totalPages,
                hasPrev: this.currentPage > 1
            });

            // Update results count
            this.updateResultsCount(totalItems, startIndex + 1, Math.min(endIndex, totalItems));

        } catch (error) {
            console.error('Error loading products:', error);
            this.showAlert('Error loading products. Please try again.', 'danger');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Load categories for filters and forms
     */
    loadCategories() {
        const categories = this.dataService.getCategories();
        this.renderCategoryFilter(categories);
        this.renderCategoryOptions(categories);
    }

    /**
     * Handle search functionality
     */
    handleSearch(query) {
        this.currentSearchQuery = query;
        this.currentPage = 1;
        this.loadProducts();
    }

    /**
     * Handle category filter
     */
    handleCategoryFilter(category) {
        if (category) {
            this.currentFilters.category = category;
        } else {
            delete this.currentFilters.category;
        }
        this.currentPage = 1;
        this.loadProducts();
    }

    /**
     * Handle price filter
     */
    handlePriceFilter(minPrice, maxPrice) {
        if (minPrice) {
            this.currentFilters.minPrice = parseFloat(minPrice);
        } else {
            delete this.currentFilters.minPrice;
        }

        if (maxPrice) {
            this.currentFilters.maxPrice = parseFloat(maxPrice);
        } else {
            delete this.currentFilters.maxPrice;
        }

        this.currentPage = 1;
        this.loadProducts();
    }

    /**
     * Handle stock filter
     */
    handleStockFilter(inStockOnly) {
        if (inStockOnly) {
            this.currentFilters.inStock = true;
        } else {
            delete this.currentFilters.inStock;
        }
        this.currentPage = 1;
        this.loadProducts();
    }

    /**
     * Handle sorting
     */
    handleSort(sortBy) {
        this.currentSort = sortBy;
        this.loadProducts();
    }

    /**
     * Sort products based on current sort option
     */
    sortProducts(products) {
        if (!this.currentSort) return products;

        const [field, direction] = this.currentSort.split('-');

        return [...products].sort((a, b) => {
            let aValue = a[field];
            let bValue = b[field];

            // Handle different data types
            if (field === 'price' || field === 'rating') {
                aValue = parseFloat(aValue) || 0;
                bValue = parseFloat(bValue) || 0;
            } else if (field === 'createdAt' || field === 'updatedAt') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            } else {
                aValue = String(aValue).toLowerCase();
                bValue = String(bValue).toLowerCase();
            }

            if (direction === 'desc') {
                return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
            } else {
                return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
            }
        });
    }

    /**
     * Handle add product
     */
    async handleAddProduct(formData) {
        try {
            this.setLoading(true);

            const productData = this.formDataToObject(formData);

            // Validate required fields
            const validation = this.validateProductData(productData);
            if (!validation.isValid) {
                this.showFormErrors('addProductForm', validation.errors);
                return;
            }

            // Add product
            const newProduct = this.dataService.addProduct(productData);

            // Close modal and refresh products
            const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
            if (modal) modal.hide();

            this.loadProducts();
            this.showAlert('Product added successfully!', 'success');

        } catch (error) {
            console.error('Error adding product:', error);
            this.showAlert(error.message || 'Error adding product. Please try again.', 'danger');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Handle edit product
     */
    async handleEditProduct(formData) {
        try {
            this.setLoading(true);

            const productData = this.formDataToObject(formData);
            const productId = formData.get('id');

            if (!productId) {
                throw new Error('Product ID is required');
            }

            // Validate required fields
            const validation = this.validateProductData(productData);
            if (!validation.isValid) {
                this.showFormErrors('editProductForm', validation.errors);
                return;
            }

            // Update product
            const updatedProduct = this.dataService.updateProduct(productId, productData);

            // Close modal and refresh products
            const modal = bootstrap.Modal.getInstance(document.getElementById('editProductModal'));
            if (modal) modal.hide();

            this.loadProducts();
            this.showAlert('Product updated successfully!', 'success');

        } catch (error) {
            console.error('Error updating product:', error);
            this.showAlert(error.message || 'Error updating product. Please try again.', 'danger');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Handle delete product
     */
    async handleDeleteProduct(productId) {
        if (!confirm('Are you sure you want to delete this product?')) {
            return;
        }

        try {
            this.setLoading(true);

            this.dataService.deleteProduct(productId);
            this.loadProducts();
            this.showAlert('Product deleted successfully!', 'success');

        } catch (error) {
            console.error('Error deleting product:', error);
            this.showAlert(error.message || 'Error deleting product. Please try again.', 'danger');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Handle pagination
     */
    handlePagination(page) {
        this.currentPage = page;
        this.loadProducts();

        // Scroll to top of products
        const productsContainer = document.getElementById('productsContainer');
        if (productsContainer) {
            productsContainer.scrollIntoView({ behavior: 'smooth' });
        }
    }

    /**
     * Clear all filters
     */
    clearFilters() {
        this.currentFilters = {};
        this.currentSearchQuery = '';
        this.currentPage = 1;

        // Reset form inputs
        const searchInput = document.getElementById('searchInput');
        if (searchInput) searchInput.value = '';

        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) categoryFilter.value = '';

        const priceMinFilter = document.getElementById('priceMinFilter');
        if (priceMinFilter) priceMinFilter.value = '';

        const priceMaxFilter = document.getElementById('priceMaxFilter');
        if (priceMaxFilter) priceMaxFilter.value = '';

        const inStockFilter = document.getElementById('inStockFilter');
        if (inStockFilter) inStockFilter.checked = false;

        this.loadProducts();
    }

    /**
     * Set view mode (grid or list)
     */
    setView(viewMode) {
        const productsContainer = document.getElementById('productsContainer');
        if (!productsContainer) return;

        // Update container classes
        if (viewMode === 'list') {
            productsContainer.classList.remove('row');
            productsContainer.classList.add('mm-list-view');
        } else {
            productsContainer.classList.add('row');
            productsContainer.classList.remove('mm-list-view');
        }

        // Update button states
        const gridViewBtn = document.getElementById('gridViewBtn');
        const listViewBtn = document.getElementById('listViewBtn');

        if (gridViewBtn && listViewBtn) {
            if (viewMode === 'grid') {
                gridViewBtn.classList.add('active');
                listViewBtn.classList.remove('active');
            } else {
                listViewBtn.classList.add('active');
                gridViewBtn.classList.remove('active');
            }
        }

        // Re-render products with new view
        this.loadProducts();
    }

    /**
     * Convert FormData to object
     */
    formDataToObject(formData) {
        const obj = {};
        for (let [key, value] of formData.entries()) {
            // Handle checkboxes and special fields
            if (key === 'inStock') {
                obj[key] = value === 'on' || value === 'true';
            } else if (key === 'price' || key === 'rating') {
                obj[key] = parseFloat(value) || 0;
            } else if (key === 'quantity' || key === 'reviews') {
                obj[key] = parseInt(value) || 0;
            } else if (key === 'tags') {
                obj[key] = value.split(',').map(tag => tag.trim()).filter(tag => tag);
            } else {
                obj[key] = value;
            }
        }
        return obj;
    }

    /**
     * Validate product data
     */
    validateProductData(productData) {
        const errors = [];
        const config = this.modelMaster.config;
        const validation = config?.validation?.product;

        if (!validation) {
            return { isValid: true, errors: [] };
        }

        // Validate name
        if (validation.name?.required && !productData.name?.trim()) {
            errors.push('Product name is required');
        }
        if (productData.name && productData.name.length < validation.name?.minLength) {
            errors.push(`Product name must be at least ${validation.name.minLength} characters`);
        }
        if (productData.name && productData.name.length > validation.name?.maxLength) {
            errors.push(`Product name must not exceed ${validation.name.maxLength} characters`);
        }

        // Validate price
        if (validation.price?.required && (productData.price === null || productData.price === undefined || productData.price === '')) {
            errors.push('Product price is required');
        }
        if (productData.price < validation.price?.min) {
            errors.push(`Product price must be at least ${validation.price.min}`);
        }
        if (productData.price > validation.price?.max) {
            errors.push(`Product price must not exceed ${validation.price.max}`);
        }

        // Validate category
        if (validation.category?.required && !productData.category?.trim()) {
            errors.push('Product category is required');
        }

        // Validate SKU
        if (validation.sku?.required && !productData.sku?.trim()) {
            errors.push('Product SKU is required');
        }
        if (productData.sku && validation.sku?.pattern && !new RegExp(validation.sku.pattern).test(productData.sku)) {
            errors.push('Product SKU format is invalid (use uppercase letters, numbers, and hyphens only)');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * Setup form validation
     */
    setupFormValidation() {
        // Real-time validation for forms
        const forms = document.querySelectorAll('form[data-validate="true"]');
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
                input.addEventListener('input', () => {
                    this.clearFieldError(input);
                });
            });
        });
    }

    /**
     * Validate individual field
     */
    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.name;
        const config = this.modelMaster.config;
        const validation = config?.validation?.product?.[fieldName];

        if (!validation) return true;

        let isValid = true;
        let errorMessage = '';

        // Required validation
        if (validation.required && !value) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(fieldName)} is required`;
        }

        // Length validation
        if (value && validation.minLength && value.length < validation.minLength) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(fieldName)} must be at least ${validation.minLength} characters`;
        }

        if (value && validation.maxLength && value.length > validation.maxLength) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(fieldName)} must not exceed ${validation.maxLength} characters`;
        }

        // Pattern validation
        if (value && validation.pattern && !new RegExp(validation.pattern).test(value)) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(fieldName)} format is invalid`;
        }

        // Number validation
        if (fieldName === 'price' && value) {
            const numValue = parseFloat(value);
            if (isNaN(numValue)) {
                isValid = false;
                errorMessage = 'Price must be a valid number';
            } else if (validation.min !== undefined && numValue < validation.min) {
                isValid = false;
                errorMessage = `Price must be at least ${validation.min}`;
            } else if (validation.max !== undefined && numValue > validation.max) {
                isValid = false;
                errorMessage = `Price must not exceed ${validation.max}`;
            }
        }

        // Show/hide error
        if (isValid) {
            this.clearFieldError(field);
        } else {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    /**
     * Get field label for error messages
     */
    getFieldLabel(fieldName) {
        const labels = {
            name: 'Product name',
            description: 'Description',
            price: 'Price',
            category: 'Category',
            sku: 'SKU',
            quantity: 'Quantity'
        };
        return labels[fieldName] || fieldName;
    }

    /**
     * Show field error
     */
    showFieldError(field, message) {
        field.classList.add('is-invalid');

        let errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            field.parentNode.appendChild(errorDiv);
        }
        errorDiv.textContent = message;
    }

    /**
     * Clear field error
     */
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    /**
     * Show form errors
     */
    showFormErrors(formId, errors) {
        const form = document.getElementById(formId);
        if (!form) return;

        // Clear previous errors
        form.querySelectorAll('.is-invalid').forEach(field => {
            this.clearFieldError(field);
        });

        // Show new errors
        if (errors.length > 0) {
            this.showAlert(errors.join('<br>'), 'danger');
        }
    }

    /**
     * Reset add product form
     */
    resetAddProductForm() {
        const form = document.getElementById('addProductForm');
        if (form) {
            form.reset();
            form.querySelectorAll('.is-invalid').forEach(field => {
                this.clearFieldError(field);
            });
        }
    }

    /**
     * Set loading state
     */
    setLoading(isLoading) {
        this.isLoading = isLoading;

        const loadingIndicator = document.getElementById('loadingIndicator');
        const productsContainer = document.getElementById('productsContainer');

        if (loadingIndicator) {
            loadingIndicator.style.display = isLoading ? 'flex' : 'none';
        }

        if (productsContainer) {
            productsContainer.style.opacity = isLoading ? '0.5' : '1';
        }

        // Disable/enable buttons
        const buttons = document.querySelectorAll('button, .btn');
        buttons.forEach(btn => {
            btn.disabled = isLoading;
        });
    }

    /**
     * Show alert message
     */
    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) return;

        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show mm-alert mm-alert-${type}`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        alertContainer.appendChild(alertDiv);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    /**
     * Update results count display
     */
    updateResultsCount(total, start, end) {
        const resultsCount = document.getElementById('resultsCount');
        if (resultsCount) {
            if (total === 0) {
                resultsCount.textContent = 'No products found';
            } else {
                resultsCount.textContent = `Showing ${start}-${end} of ${total} products`;
            }
        }
    }

    /**
     * Render products in the container
     */
    renderProducts(products) {
        const productsContainer = document.getElementById('productsContainer');
        if (!productsContainer) return;

        if (products.length === 0) {
            productsContainer.innerHTML = `
                <div class="col-12">
                    <div class="mm-empty-state">
                        <div class="mm-empty-state-icon">
                            <i class="bi bi-box-seam"></i>
                        </div>
                        <h4>No products found</h4>
                        <p class="mm-text-muted">Try adjusting your search or filters to find what you're looking for.</p>
                        <button type="button" class="mm-btn mm-btn-primary" onclick="productController.clearFilters()">
                            Clear Filters
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        const config = this.modelMaster.config;
        const gridColumns = config?.ui?.gridColumns || {
            xs: 12, sm: 6, md: 4, lg: 3, xl: 2, xxl: 2
        };

        const isListView = productsContainer.classList.contains('mm-list-view');

        const productsHTML = products.map(product => {
            const categoryInfo = this.dataService.getCategoryById(product.categoryId);
            const categoryIcon = categoryInfo?.icon || 'bi-tag';

            if (isListView) {
                return this.renderProductListItem(product, categoryIcon);
            } else {
                return this.renderProductCard(product, categoryIcon, gridColumns);
            }
        }).join('');

        productsContainer.innerHTML = productsHTML;

        // Add animation classes
        setTimeout(() => {
            productsContainer.querySelectorAll('.mm-card').forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('mm-fade-in');
                }, index * 50);
            });
        }, 10);
    }

    /**
     * Render individual product card
     */
    renderProductCard(product, categoryIcon, gridColumns) {
        const currency = this.modelMaster.config?.config?.currency || 'USD';
        const currencySymbol = currency === 'USD' ? '$' : currency;

        return `
            <div class="col-${gridColumns.xs} col-sm-${gridColumns.sm} col-md-${gridColumns.md} col-lg-${gridColumns.lg} col-xl-${gridColumns.xl} col-xxl-${gridColumns.xxl}">
                <div class="mm-card mm-product-card">
                    <img src="${product.image}" alt="${product.name}" class="mm-product-image" loading="lazy">
                    <div class="mm-product-info">
                        <a href="#" class="mm-product-title" onclick="productController.viewProduct('${product.id}')">${product.name}</a>
                        <p class="mm-product-description">${product.description}</p>
                        <div class="mm-product-meta">
                            <a href="#" class="mm-product-category" onclick="productController.filterByCategory('${product.category}')">
                                <i class="${categoryIcon}"></i> ${product.category}
                            </a>
                            <div class="mm-product-rating">
                                <i class="bi bi-star-fill"></i>
                                <span>${product.rating}</span>
                                <small class="mm-text-muted">(${product.reviews})</small>
                            </div>
                        </div>
                        <div class="mm-product-price">${currencySymbol}${product.price.toFixed(2)}</div>
                        <div class="mm-product-stock ${product.inStock ? 'mm-text-success' : 'mm-text-danger'}">
                            <i class="bi ${product.inStock ? 'bi-check-circle' : 'bi-x-circle'}"></i>
                            ${product.inStock ? `In Stock (${product.quantity})` : 'Out of Stock'}
                        </div>
                        <div class="mm-product-actions">
                            <button type="button" class="mm-btn mm-btn-primary mm-btn-sm" onclick="productController.editProduct('${product.id}')">
                                <i class="bi bi-pencil"></i> Edit
                            </button>
                            <button type="button" class="mm-btn mm-btn-outline-primary mm-btn-sm" onclick="productController.viewProduct('${product.id}')">
                                <i class="bi bi-eye"></i> View
                            </button>
                            <button type="button" class="mm-btn mm-btn-secondary mm-btn-sm" onclick="productController.handleDeleteProduct('${product.id}')">
                                <i class="bi bi-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render individual product list item
     */
    renderProductListItem(product, categoryIcon) {
        const currency = this.modelMaster.config?.config?.currency || 'USD';
        const currencySymbol = currency === 'USD' ? '$' : currency;

        return `
            <div class="col-12">
                <div class="mm-card mm-product-card mm-d-flex mm-flex-row">
                    <img src="${product.image}" alt="${product.name}" class="mm-product-image" style="width: 200px; height: 150px;" loading="lazy">
                    <div class="mm-product-info mm-flex-row mm-w-100">
                        <div class="mm-flex-column" style="flex: 1;">
                            <a href="#" class="mm-product-title" onclick="productController.viewProduct('${product.id}')">${product.name}</a>
                            <p class="mm-product-description">${product.description}</p>
                            <div class="mm-product-meta">
                                <a href="#" class="mm-product-category" onclick="productController.filterByCategory('${product.category}')">
                                    <i class="${categoryIcon}"></i> ${product.category}
                                </a>
                                <div class="mm-product-rating">
                                    <i class="bi bi-star-fill"></i>
                                    <span>${product.rating}</span>
                                    <small class="mm-text-muted">(${product.reviews})</small>
                                </div>
                            </div>
                        </div>
                        <div class="mm-d-flex mm-flex-column mm-align-items-end mm-justify-content-between" style="min-width: 200px;">
                            <div class="mm-product-price">${currencySymbol}${product.price.toFixed(2)}</div>
                            <div class="mm-product-stock ${product.inStock ? 'mm-text-success' : 'mm-text-danger'}">
                                <i class="bi ${product.inStock ? 'bi-check-circle' : 'bi-x-circle'}"></i>
                                ${product.inStock ? `In Stock (${product.quantity})` : 'Out of Stock'}
                            </div>
                            <div class="mm-product-actions">
                                <button type="button" class="mm-btn mm-btn-primary mm-btn-sm" onclick="productController.editProduct('${product.id}')">
                                    <i class="bi bi-pencil"></i> Edit
                                </button>
                                <button type="button" class="mm-btn mm-btn-outline-primary mm-btn-sm" onclick="productController.viewProduct('${product.id}')">
                                    <i class="bi bi-eye"></i> View
                                </button>
                                <button type="button" class="mm-btn mm-btn-secondary mm-btn-sm" onclick="productController.handleDeleteProduct('${product.id}')">
                                    <i class="bi bi-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render pagination
     */
    renderPagination(paginationInfo) {
        const paginationContainer = document.getElementById('paginationContainer');
        if (!paginationContainer) return;

        if (paginationInfo.totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        const { currentPage, totalPages, hasNext, hasPrev } = paginationInfo;
        let paginationHTML = '<nav aria-label="Products pagination"><div class="mm-pagination">';

        // Previous button
        paginationHTML += `
            <a href="#" class="mm-pagination-item ${!hasPrev ? 'disabled' : ''}"
               onclick="${hasPrev ? `productController.handlePagination(${currentPage - 1})` : 'return false'}"
               aria-label="Previous">
                <i class="bi bi-chevron-left"></i>
            </a>
        `;

        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `
                <a href="#" class="mm-pagination-item" onclick="productController.handlePagination(1)">1</a>
            `;
            if (startPage > 2) {
                paginationHTML += '<span class="mm-pagination-item disabled">...</span>';
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <a href="#" class="mm-pagination-item ${i === currentPage ? 'active' : ''}"
                   onclick="productController.handlePagination(${i})">${i}</a>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += '<span class="mm-pagination-item disabled">...</span>';
            }
            paginationHTML += `
                <a href="#" class="mm-pagination-item" onclick="productController.handlePagination(${totalPages})">${totalPages}</a>
            `;
        }

        // Next button
        paginationHTML += `
            <a href="#" class="mm-pagination-item ${!hasNext ? 'disabled' : ''}"
               onclick="${hasNext ? `productController.handlePagination(${currentPage + 1})` : 'return false'}"
               aria-label="Next">
                <i class="bi bi-chevron-right"></i>
            </a>
        `;

        paginationHTML += '</div></nav>';
        paginationContainer.innerHTML = paginationHTML;
    }

    /**
     * Render category filter options
     */
    renderCategoryFilter(categories) {
        const categoryFilter = document.getElementById('categoryFilter');
        if (!categoryFilter) return;

        let optionsHTML = '<option value="">All Categories</option>';
        categories.forEach(category => {
            optionsHTML += `<option value="${category.name}">${category.name}</option>`;
        });

        categoryFilter.innerHTML = optionsHTML;
    }

    /**
     * Render category options for forms
     */
    renderCategoryOptions(categories) {
        const categorySelects = document.querySelectorAll('select[name="category"]');

        categorySelects.forEach(select => {
            let optionsHTML = '<option value="">Select Category</option>';
            categories.forEach(category => {
                optionsHTML += `<option value="${category.name}">${category.name}</option>`;
            });
            select.innerHTML = optionsHTML;
        });
    }

    /**
     * View product details
     */
    viewProduct(productId) {
        const product = this.dataService.getProductById(productId);
        if (!product) {
            this.showAlert('Product not found', 'danger');
            return;
        }

        // You can implement a modal or navigate to a detail page
        // For now, we'll show an alert with product info
        const productInfo = `
            <strong>${product.name}</strong><br>
            Price: $${product.price}<br>
            Category: ${product.category}<br>
            SKU: ${product.sku}<br>
            ${product.description}
        `;

        this.showAlert(productInfo, 'info');
    }

    /**
     * Edit product
     */
    editProduct(productId) {
        const product = this.dataService.getProductById(productId);
        if (!product) {
            this.showAlert('Product not found', 'danger');
            return;
        }

        // Populate edit form
        const editForm = document.getElementById('editProductForm');
        if (editForm) {
            editForm.querySelector('[name="id"]').value = product.id;
            editForm.querySelector('[name="name"]').value = product.name;
            editForm.querySelector('[name="description"]').value = product.description;
            editForm.querySelector('[name="price"]').value = product.price;
            editForm.querySelector('[name="category"]').value = product.category;
            editForm.querySelector('[name="sku"]').value = product.sku;
            editForm.querySelector('[name="quantity"]').value = product.quantity;
            editForm.querySelector('[name="image"]').value = product.image;
            editForm.querySelector('[name="inStock"]').checked = product.inStock;
            editForm.querySelector('[name="tags"]').value = product.tags.join(', ');

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editProductModal'));
            modal.show();
        }
    }

    /**
     * Filter by category
     */
    filterByCategory(category) {
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.value = category;
            this.handleCategoryFilter(category);
        }
    }
}

/**
 * View Renderer Class for handling UI rendering
 */
class ViewRenderer {
    constructor(productController) {
        this.productController = productController;
    }

    /**
     * Render the main application layout
     */
    renderLayout() {
        // This would be called if we were building the entire layout dynamically
        // For now, we assume the HTML structure exists
    }

    /**
     * Render loading spinner
     */
    renderLoading() {
        return `
            <div class="mm-loading">
                <div class="mm-spinner"></div>
                <span class="ms-2">Loading...</span>
            </div>
        `;
    }

    /**
     * Render error message
     */
    renderError(message) {
        return `
            <div class="mm-alert mm-alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                ${message}
            </div>
        `;
    }
}

// Global variables for application initialization
let modelMaster;
let dataService;
let productController;
let viewRenderer;

/**
 * Initialize the Model Master application
 */
async function initializeModelMaster() {
    try {
        // Initialize core classes
        modelMaster = new ModelMaster();

        // Wait for configuration to load
        await modelMaster.init();

        // Initialize services and controllers
        dataService = new DataService(modelMaster);
        productController = new ProductController(modelMaster, dataService);
        viewRenderer = new ViewRenderer(productController);

        // Load data from localStorage if available
        dataService.loadFromStorage();

        console.log('Model Master initialized successfully');

    } catch (error) {
        console.error('Failed to initialize Model Master:', error);

        // Show error message to user
        const alertContainer = document.getElementById('alertContainer');
        if (alertContainer) {
            alertContainer.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Initialization Error:</strong> ${error.message}
                </div>
            `;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeModelMaster);