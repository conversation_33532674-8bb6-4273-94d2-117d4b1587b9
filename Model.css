/**
 * Model Master - Responsive CSS Framework
 * No hardcoded values - all using CSS custom properties and Bootstrap integration
 * Mobile-first responsive design
 */

/* CSS Custom Properties (Variables) */
:root {
  /* Colors - Using Bootstrap CSS variables */
  --mm-primary: var(--bs-primary, #0d6efd);
  --mm-secondary: var(--bs-secondary, #6c757d);
  --mm-success: var(--bs-success, #198754);
  --mm-danger: var(--bs-danger, #dc3545);
  --mm-warning: var(--bs-warning, #ffc107);
  --mm-info: var(--bs-info, #0dcaf0);
  --mm-light: var(--bs-light, #f8f9fa);
  --mm-dark: var(--bs-dark, #212529);

  /* Background Colors */
  --mm-bg-primary: var(--bs-primary-bg-subtle, rgba(13, 110, 253, 0.1));
  --mm-bg-secondary: var(--bs-secondary-bg-subtle, rgba(108, 117, 125, 0.1));
  --mm-bg-success: var(--bs-success-bg-subtle, rgba(25, 135, 84, 0.1));
  --mm-bg-danger: var(--bs-danger-bg-subtle, rgba(220, 53, 69, 0.1));
  --mm-bg-warning: var(--bs-warning-bg-subtle, rgba(255, 193, 7, 0.1));
  --mm-bg-info: var(--bs-info-bg-subtle, rgba(13, 202, 240, 0.1));

  /* Text Colors */
  --mm-text-primary: var(--bs-primary-text-emphasis, #052c65);
  --mm-text-secondary: var(--bs-secondary-text-emphasis, #2b2f32);
  --mm-text-success: var(--bs-success-text-emphasis, #0a3622);
  --mm-text-danger: var(--bs-danger-text-emphasis, #58151c);
  --mm-text-warning: var(--bs-warning-text-emphasis, #664d03);
  --mm-text-info: var(--bs-info-text-emphasis, #055160);
  --mm-text-light: var(--bs-light-text-emphasis, #495057);
  --mm-text-dark: var(--bs-dark-text-emphasis, #495057);
  --mm-text-body: var(--bs-body-color, #212529);
  --mm-text-muted: var(--bs-secondary-color, #6c757d);

  /* Spacing */
  --mm-spacer: 1rem;
  --mm-spacer-xs: calc(var(--mm-spacer) * 0.25);
  --mm-spacer-sm: calc(var(--mm-spacer) * 0.5);
  --mm-spacer-md: var(--mm-spacer);
  --mm-spacer-lg: calc(var(--mm-spacer) * 1.5);
  --mm-spacer-xl: calc(var(--mm-spacer) * 3);

  /* Border Radius */
  --mm-border-radius: var(--bs-border-radius, 0.375rem);
  --mm-border-radius-sm: var(--bs-border-radius-sm, 0.25rem);
  --mm-border-radius-lg: var(--bs-border-radius-lg, 0.5rem);
  --mm-border-radius-xl: var(--bs-border-radius-xl, 1rem);
  --mm-border-radius-pill: var(--bs-border-radius-pill, 50rem);

  /* Borders */
  --mm-border-width: var(--bs-border-width, 1px);
  --mm-border-color: var(--bs-border-color, #dee2e6);
  --mm-border: var(--mm-border-width) solid var(--mm-border-color);

  /* Shadows */
  --mm-box-shadow: var(--bs-box-shadow, 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075));
  --mm-box-shadow-sm: var(--bs-box-shadow-sm, 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075));
  --mm-box-shadow-lg: var(--bs-box-shadow-lg, 0 1rem 3rem rgba(0, 0, 0, 0.175));

  /* Typography */
  --mm-font-family: var(--bs-font-sans-serif, system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif);
  --mm-font-size-base: var(--bs-font-size-base, 1rem);
  --mm-font-size-sm: var(--bs-font-size-sm, 0.875rem);
  --mm-font-size-lg: var(--bs-font-size-lg, 1.25rem);
  --mm-line-height-base: var(--bs-line-height-base, 1.5);
  --mm-font-weight-light: var(--bs-font-weight-light, 300);
  --mm-font-weight-normal: var(--bs-font-weight-normal, 400);
  --mm-font-weight-bold: var(--bs-font-weight-bold, 700);

  /* Transitions */
  --mm-transition: var(--bs-transition, color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out);
  --mm-transition-fast: all 0.1s ease-in-out;
  --mm-transition-slow: all 0.3s ease-in-out;

  /* Z-index */
  --mm-zindex-dropdown: var(--bs-zindex-dropdown, 1000);
  --mm-zindex-sticky: var(--bs-zindex-sticky, 1020);
  --mm-zindex-fixed: var(--bs-zindex-fixed, 1030);
  --mm-zindex-modal-backdrop: var(--bs-zindex-modal-backdrop, 1040);
  --mm-zindex-modal: var(--bs-zindex-modal, 1055);
  --mm-zindex-popover: var(--bs-zindex-popover, 1070);
  --mm-zindex-tooltip: var(--bs-zindex-tooltip, 1080);

  /* Custom Model Master Variables */
  --mm-card-padding: var(--mm-spacer-lg);
  --mm-card-margin: var(--mm-spacer-md);
  --mm-product-image-height: 200px;
  --mm-product-image-height-sm: 150px;
  --mm-product-image-height-lg: 250px;
  --mm-header-height: 80px;
  --mm-sidebar-width: 280px;
  --mm-sidebar-width-collapsed: 60px;
  --mm-animation-duration: 0.3s;
  --mm-animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--mm-font-family);
  font-size: var(--mm-font-size-base);
  line-height: var(--mm-line-height-base);
  color: var(--mm-text-body);
  background-color: var(--mm-light);
  margin: 0;
  padding: 0;
}

/* Model Master Layout Components */
.mm-container {
  width: 100%;
  padding-right: var(--mm-spacer-md);
  padding-left: var(--mm-spacer-md);
  margin-right: auto;
  margin-left: auto;
}

.mm-header {
  background-color: var(--mm-primary);
  color: white;
  padding: var(--mm-spacer-md) 0;
  box-shadow: var(--mm-box-shadow);
  position: sticky;
  top: 0;
  z-index: var(--mm-zindex-sticky);
}

.mm-main {
  min-height: calc(100vh - var(--mm-header-height));
  padding: var(--mm-spacer-lg) 0;
}

.mm-sidebar {
  background-color: white;
  border-right: var(--mm-border);
  padding: var(--mm-spacer-lg);
  height: 100%;
  transition: var(--mm-transition);
}

/* Card Components */
.mm-card {
  background-color: white;
  border: var(--mm-border);
  border-radius: var(--mm-border-radius);
  box-shadow: var(--mm-box-shadow-sm);
  margin-bottom: var(--mm-card-margin);
  transition: var(--mm-transition);
  overflow: hidden;
}

.mm-card:hover {
  box-shadow: var(--mm-box-shadow-lg);
  transform: translateY(-2px);
}

.mm-card-header {
  padding: var(--mm-spacer-md) var(--mm-card-padding);
  background-color: var(--mm-bg-light);
  border-bottom: var(--mm-border);
  font-weight: var(--mm-font-weight-bold);
}

.mm-card-body {
  padding: var(--mm-card-padding);
}

.mm-card-footer {
  padding: var(--mm-spacer-md) var(--mm-card-padding);
  background-color: var(--mm-bg-light);
  border-top: var(--mm-border);
}

/* Product Card Specific */
.mm-product-card {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mm-product-image {
  width: 100%;
  height: var(--mm-product-image-height);
  object-fit: cover;
  border-bottom: var(--mm-border);
}

.mm-product-info {
  padding: var(--mm-spacer-md);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.mm-product-title {
  font-size: var(--mm-font-size-lg);
  font-weight: var(--mm-font-weight-bold);
  margin-bottom: var(--mm-spacer-sm);
  color: var(--mm-text-dark);
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.mm-product-title:hover {
  color: var(--mm-primary);
  text-decoration: none;
}

.mm-product-description {
  color: var(--mm-text-muted);
  font-size: var(--mm-font-size-sm);
  margin-bottom: var(--mm-spacer-md);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
}

.mm-product-price {
  font-size: var(--mm-font-size-lg);
  font-weight: var(--mm-font-weight-bold);
  color: var(--mm-success);
  margin-bottom: var(--mm-spacer-sm);
}

.mm-product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--mm-spacer-md);
}

.mm-product-category {
  background-color: var(--mm-bg-primary);
  color: var(--mm-text-primary);
  padding: var(--mm-spacer-xs) var(--mm-spacer-sm);
  border-radius: var(--mm-border-radius-pill);
  font-size: var(--mm-font-size-sm);
  text-decoration: none;
}

.mm-product-rating {
  display: flex;
  align-items: center;
  gap: var(--mm-spacer-xs);
  color: var(--mm-warning);
}

.mm-product-actions {
  display: flex;
  gap: var(--mm-spacer-sm);
  margin-top: auto;
}

/* Button Components */
.mm-btn {
  display: inline-block;
  padding: var(--mm-spacer-sm) var(--mm-spacer-md);
  font-size: var(--mm-font-size-base);
  font-weight: var(--mm-font-weight-normal);
  line-height: var(--mm-line-height-base);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  border: var(--mm-border-width) solid transparent;
  border-radius: var(--mm-border-radius);
  transition: var(--mm-transition);
  user-select: none;
}

.mm-btn:hover {
  text-decoration: none;
  transform: translateY(-1px);
}

.mm-btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(var(--mm-primary), 0.25);
}

.mm-btn-primary {
  color: white;
  background-color: var(--mm-primary);
  border-color: var(--mm-primary);
}

.mm-btn-primary:hover {
  background-color: var(--mm-primary);
  border-color: var(--mm-primary);
  filter: brightness(0.9);
}

.mm-btn-secondary {
  color: white;
  background-color: var(--mm-secondary);
  border-color: var(--mm-secondary);
}

.mm-btn-outline-primary {
  color: var(--mm-primary);
  background-color: transparent;
  border-color: var(--mm-primary);
}

.mm-btn-outline-primary:hover {
  color: white;
  background-color: var(--mm-primary);
  border-color: var(--mm-primary);
}

.mm-btn-sm {
  padding: var(--mm-spacer-xs) var(--mm-spacer-sm);
  font-size: var(--mm-font-size-sm);
  border-radius: var(--mm-border-radius-sm);
}

.mm-btn-lg {
  padding: var(--mm-spacer-md) var(--mm-spacer-lg);
  font-size: var(--mm-font-size-lg);
  border-radius: var(--mm-border-radius-lg);
}

/* Form Components */
.mm-form-group {
  margin-bottom: var(--mm-spacer-md);
}

.mm-form-label {
  display: block;
  margin-bottom: var(--mm-spacer-xs);
  font-weight: var(--mm-font-weight-bold);
  color: var(--mm-text-dark);
}

.mm-form-control {
  display: block;
  width: 100%;
  padding: var(--mm-spacer-sm) var(--mm-spacer-md);
  font-size: var(--mm-font-size-base);
  line-height: var(--mm-line-height-base);
  color: var(--mm-text-body);
  background-color: white;
  border: var(--mm-border);
  border-radius: var(--mm-border-radius);
  transition: var(--mm-transition);
}

.mm-form-control:focus {
  outline: 0;
  border-color: var(--mm-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--mm-primary), 0.25);
}

.mm-form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--mm-spacer-sm) center;
  background-size: 16px 12px;
  padding-right: calc(var(--mm-spacer-lg) + 16px);
}

/* Search and Filter Components */
.mm-search-bar {
  position: relative;
  margin-bottom: var(--mm-spacer-lg);
}

.mm-search-input {
  padding-left: calc(var(--mm-spacer-lg) + 20px);
}

.mm-search-icon {
  position: absolute;
  left: var(--mm-spacer-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--mm-text-muted);
  pointer-events: none;
}

.mm-filters {
  background-color: white;
  border: var(--mm-border);
  border-radius: var(--mm-border-radius);
  padding: var(--mm-spacer-lg);
  margin-bottom: var(--mm-spacer-lg);
}

.mm-filter-group {
  margin-bottom: var(--mm-spacer-md);
}

.mm-filter-group:last-child {
  margin-bottom: 0;
}

/* Pagination */
.mm-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--mm-spacer-sm);
  margin-top: var(--mm-spacer-xl);
}

.mm-pagination-item {
  padding: var(--mm-spacer-sm) var(--mm-spacer-md);
  border: var(--mm-border);
  border-radius: var(--mm-border-radius);
  background-color: white;
  color: var(--mm-text-body);
  text-decoration: none;
  transition: var(--mm-transition);
}

.mm-pagination-item:hover {
  background-color: var(--mm-bg-primary);
  color: var(--mm-text-primary);
  text-decoration: none;
}

.mm-pagination-item.active {
  background-color: var(--mm-primary);
  color: white;
  border-color: var(--mm-primary);
}

.mm-pagination-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* Loading and States */
.mm-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--mm-spacer-xl);
  color: var(--mm-text-muted);
}

.mm-spinner {
  width: 2rem;
  height: 2rem;
  border: 0.25em solid var(--mm-border-color);
  border-right-color: transparent;
  border-radius: 50%;
  animation: mm-spin 0.75s linear infinite;
}

@keyframes mm-spin {
  to {
    transform: rotate(360deg);
  }
}

.mm-empty-state {
  text-align: center;
  padding: var(--mm-spacer-xl);
  color: var(--mm-text-muted);
}

.mm-empty-state-icon {
  font-size: 3rem;
  margin-bottom: var(--mm-spacer-md);
  opacity: 0.5;
}

/* Alert Components */
.mm-alert {
  padding: var(--mm-spacer-md);
  margin-bottom: var(--mm-spacer-md);
  border: var(--mm-border);
  border-radius: var(--mm-border-radius);
}

.mm-alert-success {
  color: var(--mm-text-success);
  background-color: var(--mm-bg-success);
  border-color: var(--mm-success);
}

.mm-alert-danger {
  color: var(--mm-text-danger);
  background-color: var(--mm-bg-danger);
  border-color: var(--mm-danger);
}

.mm-alert-warning {
  color: var(--mm-text-warning);
  background-color: var(--mm-bg-warning);
  border-color: var(--mm-warning);
}

.mm-alert-info {
  color: var(--mm-text-info);
  background-color: var(--mm-bg-info);
  border-color: var(--mm-info);
}

/* Utility Classes */
.mm-text-center { text-align: center; }
.mm-text-left { text-align: left; }
.mm-text-right { text-align: right; }
.mm-text-muted { color: var(--mm-text-muted); }
.mm-text-primary { color: var(--mm-primary); }
.mm-text-success { color: var(--mm-success); }
.mm-text-danger { color: var(--mm-danger); }
.mm-text-warning { color: var(--mm-warning); }
.mm-text-info { color: var(--mm-info); }

.mm-bg-primary { background-color: var(--mm-bg-primary); }
.mm-bg-secondary { background-color: var(--mm-bg-secondary); }
.mm-bg-success { background-color: var(--mm-bg-success); }
.mm-bg-danger { background-color: var(--mm-bg-danger); }
.mm-bg-warning { background-color: var(--mm-bg-warning); }
.mm-bg-info { background-color: var(--mm-bg-info); }
.mm-bg-light { background-color: var(--mm-light); }
.mm-bg-dark { background-color: var(--mm-dark); }

.mm-d-none { display: none; }
.mm-d-block { display: block; }
.mm-d-flex { display: flex; }
.mm-d-inline-block { display: inline-block; }

.mm-flex-column { flex-direction: column; }
.mm-flex-row { flex-direction: row; }
.mm-justify-content-center { justify-content: center; }
.mm-justify-content-between { justify-content: space-between; }
.mm-align-items-center { align-items: center; }
.mm-align-items-start { align-items: flex-start; }
.mm-align-items-end { align-items: flex-end; }

.mm-w-100 { width: 100%; }
.mm-h-100 { height: 100%; }

.mm-m-0 { margin: 0; }
.mm-m-1 { margin: var(--mm-spacer-xs); }
.mm-m-2 { margin: var(--mm-spacer-sm); }
.mm-m-3 { margin: var(--mm-spacer-md); }
.mm-m-4 { margin: var(--mm-spacer-lg); }
.mm-m-5 { margin: var(--mm-spacer-xl); }

.mm-p-0 { padding: 0; }
.mm-p-1 { padding: var(--mm-spacer-xs); }
.mm-p-2 { padding: var(--mm-spacer-sm); }
.mm-p-3 { padding: var(--mm-spacer-md); }
.mm-p-4 { padding: var(--mm-spacer-lg); }
.mm-p-5 { padding: var(--mm-spacer-xl); }

.mm-mb-0 { margin-bottom: 0; }
.mm-mb-1 { margin-bottom: var(--mm-spacer-xs); }
.mm-mb-2 { margin-bottom: var(--mm-spacer-sm); }
.mm-mb-3 { margin-bottom: var(--mm-spacer-md); }
.mm-mb-4 { margin-bottom: var(--mm-spacer-lg); }
.mm-mb-5 { margin-bottom: var(--mm-spacer-xl); }

.mm-mt-0 { margin-top: 0; }
.mm-mt-1 { margin-top: var(--mm-spacer-xs); }
.mm-mt-2 { margin-top: var(--mm-spacer-sm); }
.mm-mt-3 { margin-top: var(--mm-spacer-md); }
.mm-mt-4 { margin-top: var(--mm-spacer-lg); }
.mm-mt-5 { margin-top: var(--mm-spacer-xl); }

.mm-rounded { border-radius: var(--mm-border-radius); }
.mm-rounded-sm { border-radius: var(--mm-border-radius-sm); }
.mm-rounded-lg { border-radius: var(--mm-border-radius-lg); }
.mm-rounded-pill { border-radius: var(--mm-border-radius-pill); }

.mm-shadow { box-shadow: var(--mm-box-shadow); }
.mm-shadow-sm { box-shadow: var(--mm-box-shadow-sm); }
.mm-shadow-lg { box-shadow: var(--mm-box-shadow-lg); }

/* Responsive Utilities */
@media (max-width: 575.98px) {
  .mm-d-xs-none { display: none; }
  .mm-d-xs-block { display: block; }
  .mm-d-xs-flex { display: flex; }

  .mm-product-image {
    height: var(--mm-product-image-height-sm);
  }

  .mm-card-body {
    padding: var(--mm-spacer-md);
  }

  .mm-btn {
    padding: var(--mm-spacer-xs) var(--mm-spacer-sm);
    font-size: var(--mm-font-size-sm);
  }
}

@media (min-width: 576px) {
  .mm-d-sm-none { display: none; }
  .mm-d-sm-block { display: block; }
  .mm-d-sm-flex { display: flex; }
}

@media (min-width: 768px) {
  .mm-d-md-none { display: none; }
  .mm-d-md-block { display: block; }
  .mm-d-md-flex { display: flex; }
}

@media (min-width: 992px) {
  .mm-d-lg-none { display: none; }
  .mm-d-lg-block { display: block; }
  .mm-d-lg-flex { display: flex; }

  .mm-product-image {
    height: var(--mm-product-image-height-lg);
  }
}

@media (min-width: 1200px) {
  .mm-d-xl-none { display: none; }
  .mm-d-xl-block { display: block; }
  .mm-d-xl-flex { display: flex; }
}

@media (min-width: 1400px) {
  .mm-d-xxl-none { display: none; }
  .mm-d-xxl-block { display: block; }
  .mm-d-xxl-flex { display: flex; }
}

/* Dark Mode Support */
/* @media (prefers-color-scheme: dark) {
  :root {
    --mm-text-body: #f8f9fa;
    --mm-text-muted: #adb5bd;
    --mm-light: #212529;
    --mm-dark: #f8f9fa;
    --mm-border-color: #495057;
  } */

  body {
    background-color: #1a1a1a;
  }

  .mm-card {
    background-color: #2d3748;
    border-color: #4a5568;
  }

  .mm-form-control {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #f8f9fa;
  }

  .mm-sidebar {
    background-color: #2d3748;
    border-color: #4a5568;
  }
}

/* Print Styles */
@media print {
  .mm-btn,
  .mm-pagination,
  .mm-filters,
  .mm-search-bar {
    display: none;
  }

  .mm-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }

  .mm-product-card {
    page-break-inside: avoid;
  }
}

/* Animation Classes */
.mm-fade-in {
  animation: mmFadeIn var(--mm-animation-duration) var(--mm-animation-easing);
}

.mm-slide-up {
  animation: mmSlideUp var(--mm-animation-duration) var(--mm-animation-easing);
}

.mm-scale-in {
  animation: mmScaleIn var(--mm-animation-duration) var(--mm-animation-easing);
}

@keyframes mmFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes mmSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes mmScaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Focus Visible Support */
.mm-btn:focus-visible,
.mm-form-control:focus-visible {
  outline: 2px solid var(--mm-primary);
  outline-offset: 2px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* List View Specific Styles */
.mm-list-view .mm-product-card {
  flex-direction: row !important;
  align-items: stretch;
}

.mm-list-view .mm-product-image {
  width: 200px;
  min-width: 200px;
  height: 150px;
  flex-shrink: 0;
}

.mm-list-view .mm-product-info {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.mm-list-view .mm-product-info > div:first-child {
  flex: 1;
  padding-right: var(--mm-spacer-md);
}

.mm-list-view .mm-product-actions {
  flex-direction: column;
  min-width: 200px;
  justify-content: space-between;
}

/* Enhanced Button Styles */
.btn-group .btn.active {
  background-color: var(--mm-primary);
  border-color: var(--mm-primary);
  color: white;
}

/* Enhanced Form Styles */
.form-control:focus,
.form-select:focus {
  border-color: var(--mm-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--mm-primary), 0.25);
}

.is-invalid {
  border-color: var(--mm-danger);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: var(--mm-font-size-sm);
  color: var(--mm-danger);
}

/* Enhanced Modal Styles */
.modal-content {
  border: none;
  border-radius: var(--mm-border-radius-lg);
  box-shadow: var(--mm-box-shadow-lg);
}

.modal-header {
  border-bottom: var(--mm-border);
  background-color: var(--mm-bg-light);
}

.modal-footer {
  border-top: var(--mm-border);
  background-color: var(--mm-bg-light);
}

/* Enhanced Alert Styles */
.alert {
  border: none;
  border-radius: var(--mm-border-radius);
  box-shadow: var(--mm-box-shadow-sm);
}

.alert-dismissible .btn-close {
  padding: var(--mm-spacer-sm);
}

/* Tooltip Styles */
.tooltip {
  font-size: var(--mm-font-size-sm);
}

.tooltip-inner {
  background-color: var(--mm-dark);
  border-radius: var(--mm-border-radius-sm);
}

/* Enhanced Dropdown Styles */
.dropdown-menu {
  border: var(--mm-border);
  border-radius: var(--mm-border-radius);
  box-shadow: var(--mm-box-shadow-lg);
}

.dropdown-item {
  transition: var(--mm-transition);
}

.dropdown-item:hover {
  background-color: var(--mm-bg-primary);
  color: var(--mm-text-primary);
}

/* Enhanced Badge Styles */
.badge {
  font-size: 0.75em;
  border-radius: var(--mm-border-radius-pill);
}

/* Skeleton Loading Animation */
.mm-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: mmSkeleton 1.5s infinite;
}

@keyframes mmSkeleton {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.mm-skeleton-text {
  height: 1em;
  margin-bottom: 0.5em;
  border-radius: var(--mm-border-radius-sm);
}

.mm-skeleton-image {
  height: var(--mm-product-image-height);
  border-radius: var(--mm-border-radius);
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--mm-bg-light);
}

::-webkit-scrollbar-thumb {
  background: var(--mm-border-color);
  border-radius: var(--mm-border-radius-pill);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--mm-secondary);
}

/* Focus Ring Enhancement */
.mm-focus-ring:focus {
  outline: 2px solid var(--mm-primary);
  outline-offset: 2px;
}

/* Interactive Elements */
.mm-interactive {
  cursor: pointer;
  transition: var(--mm-transition);
}

.mm-interactive:hover {
  transform: translateY(-1px);
}

/* Status Indicators */
.mm-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--mm-spacer-xs);
  padding: var(--mm-spacer-xs) var(--mm-spacer-sm);
  border-radius: var(--mm-border-radius-pill);
  font-size: var(--mm-font-size-sm);
  font-weight: var(--mm-font-weight-bold);
}

.mm-status-in-stock {
  background-color: var(--mm-bg-success);
  color: var(--mm-text-success);
}

.mm-status-out-of-stock {
  background-color: var(--mm-bg-danger);
  color: var(--mm-text-danger);
}

.mm-status-low-stock {
  background-color: var(--mm-bg-warning);
  color: var(--mm-text-warning);
}

/* Enhanced Typography */
.mm-text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mm-text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.mm-text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced Spacing Utilities */
.mm-gap-1 { gap: var(--mm-spacer-xs); }
.mm-gap-2 { gap: var(--mm-spacer-sm); }
.mm-gap-3 { gap: var(--mm-spacer-md); }
.mm-gap-4 { gap: var(--mm-spacer-lg); }
.mm-gap-5 { gap: var(--mm-spacer-xl); }

/* Enhanced Border Utilities */
.mm-border-start { border-left: var(--mm-border); }
.mm-border-end { border-right: var(--mm-border); }
.mm-border-top { border-top: var(--mm-border); }
.mm-border-bottom { border-bottom: var(--mm-border); }

/* Enhanced Position Utilities */
.mm-position-relative { position: relative; }
.mm-position-absolute { position: absolute; }
.mm-position-fixed { position: fixed; }
.mm-position-sticky { position: sticky; }

/* Enhanced Overflow Utilities */
.mm-overflow-hidden { overflow: hidden; }
.mm-overflow-auto { overflow: auto; }
.mm-overflow-scroll { overflow: scroll; }

/* Enhanced Cursor Utilities */
.mm-cursor-pointer { cursor: pointer; }
.mm-cursor-not-allowed { cursor: not-allowed; }
.mm-cursor-grab { cursor: grab; }
.mm-cursor-grabbing { cursor: grabbing; }

/* Enhanced User Select Utilities */
.mm-user-select-none { user-select: none; }
.mm-user-select-all { user-select: all; }
.mm-user-select-auto { user-select: auto; }

/* Enhanced Pointer Events Utilities */
.mm-pointer-events-none { pointer-events: none; }
.mm-pointer-events-auto { pointer-events: auto; }

/* Enhanced Visibility Utilities */
.mm-visible { visibility: visible; }
.mm-invisible { visibility: hidden; }

/* Enhanced Opacity Utilities */
.mm-opacity-0 { opacity: 0; }
.mm-opacity-25 { opacity: 0.25; }
.mm-opacity-50 { opacity: 0.5; }
.mm-opacity-75 { opacity: 0.75; }
.mm-opacity-100 { opacity: 1; }

/* Enhanced Transform Utilities */
.mm-rotate-90 { transform: rotate(90deg); }
.mm-rotate-180 { transform: rotate(180deg); }
.mm-rotate-270 { transform: rotate(270deg); }
.mm-scale-50 { transform: scale(0.5); }
.mm-scale-75 { transform: scale(0.75); }
.mm-scale-100 { transform: scale(1); }
.mm-scale-125 { transform: scale(1.25); }
.mm-scale-150 { transform: scale(1.5); }

/* Enhanced Filter Utilities */
.mm-blur { filter: blur(4px); }
.mm-brightness-50 { filter: brightness(0.5); }
.mm-brightness-75 { filter: brightness(0.75); }
.mm-brightness-100 { filter: brightness(1); }
.mm-brightness-125 { filter: brightness(1.25); }
.mm-brightness-150 { filter: brightness(1.5); }

/* Enhanced Backdrop Filter Utilities */
.mm-backdrop-blur { backdrop-filter: blur(4px); }
.mm-backdrop-brightness-50 { backdrop-filter: brightness(0.5); }
.mm-backdrop-brightness-75 { backdrop-filter: brightness(0.75); }
.mm-backdrop-brightness-100 { backdrop-filter: brightness(1); }
.mm-backdrop-brightness-125 { backdrop-filter: brightness(1.25); }
.mm-backdrop-brightness-150 { backdrop-filter: brightness(1.5); }