<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Model Master - Product Model Management System">
    <meta name="keywords" content="product management, model master, inventory, e-commerce">
    <meta name="author" content="Model Master Team">
    <title>Model Master - Product Management System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="Model.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📦</text></svg>">
</head>
<body>
    <!-- Header -->
    <header class="mm-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-12 col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-box-seam fs-2 me-3"></i>
                        <div>
                            <h1 class="h3 mb-0">Model Master</h1>
                            <small class="opacity-75">Product Management System</small>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 mt-3 mt-md-0">
                    <div class="d-flex justify-content-md-end align-items-center gap-3">
                        <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="bi bi-plus-circle"></i>
                            <span class="d-none d-sm-inline ms-1">Add Product</span>
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-gear"></i>
                                <span class="d-none d-sm-inline ms-1">Settings</span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="bi bi-palette me-2"></i>Theme</a></li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-download me-2"></i>Export Data</a></li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-upload me-2"></i>Import Data</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-info-circle me-2"></i>About</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Alert Container -->
    <div id="alertContainer" class="container-fluid mt-3"></div>

    <!-- Main Content -->
    <main class="mm-main">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar Filters -->
                <aside class="col-12 col-lg-3 col-xl-2 mb-4">
                    <div class="mm-sidebar mm-card">
                        <h5 class="mb-3">
                            <i class="bi bi-funnel me-2"></i>Filters
                        </h5>

                        <!-- Search -->
                        <div class="mm-search-bar mb-3">
                            <div class="position-relative">
                                <i class="bi bi-search mm-search-icon"></i>
                                <input type="text" id="searchInput" class="form-control mm-search-input" placeholder="Search products...">
                            </div>
                        </div>

                        <!-- Category Filter -->
                        <div class="mm-filter-group">
                            <label for="categoryFilter" class="form-label">Category</label>
                            <select id="categoryFilter" class="form-select">
                                <option value="">All Categories</option>
                            </select>
                        </div>

                        <!-- Price Range Filter -->
                        <div class="mm-filter-group">
                            <label class="form-label">Price Range</label>
                            <div class="row g-2">
                                <div class="col-6">
                                    <input type="number" id="priceMinFilter" class="form-control" placeholder="Min" min="0" step="0.01">
                                </div>
                                <div class="col-6">
                                    <input type="number" id="priceMaxFilter" class="form-control" placeholder="Max" min="0" step="0.01">
                                </div>
                            </div>
                        </div>

                        <!-- Stock Filter -->
                        <div class="mm-filter-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="inStockFilter">
                                <label class="form-check-label" for="inStockFilter">
                                    In Stock Only
                                </label>
                            </div>
                        </div>

                        <!-- Clear Filters -->
                        <div class="mm-filter-group">
                            <button type="button" id="clearFiltersBtn" class="btn btn-outline-secondary btn-sm w-100">
                                <i class="bi bi-x-circle me-1"></i>Clear Filters
                            </button>
                        </div>
                    </div>
                </aside>

                <!-- Products Section -->
                <section class="col-12 col-lg-9 col-xl-10">
                    <!-- Toolbar -->
                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4 gap-3">
                        <div>
                            <h2 class="h4 mb-1">Products</h2>
                            <p id="resultsCount" class="text-muted mb-0">Loading products...</p>
                        </div>

                        <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center gap-3">
                            <!-- Sort Options -->
                            <div class="d-flex align-items-center gap-2">
                                <label for="sortSelect" class="form-label mb-0 text-nowrap">Sort by:</label>
                                <select id="sortSelect" class="form-select form-select-sm" style="min-width: 150px;">
                                    <option value="">Default</option>
                                    <option value="name-asc">Name (A-Z)</option>
                                    <option value="name-desc">Name (Z-A)</option>
                                    <option value="price-asc">Price (Low to High)</option>
                                    <option value="price-desc">Price (High to Low)</option>
                                    <option value="rating-desc">Rating (High to Low)</option>
                                    <option value="createdAt-desc">Newest First</option>
                                    <option value="createdAt-asc">Oldest First</option>
                                </select>
                            </div>

                            <!-- View Toggle -->
                            <div class="btn-group" role="group" aria-label="View toggle">
                                <button type="button" id="gridViewBtn" class="btn btn-outline-secondary btn-sm active">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                    <span class="d-none d-md-inline ms-1">Grid</span>
                                </button>
                                <button type="button" id="listViewBtn" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-list"></i>
                                    <span class="d-none d-md-inline ms-1">List</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Loading Indicator -->
                    <div id="loadingIndicator" class="mm-loading" style="display: none;">
                        <div class="mm-spinner"></div>
                        <span class="ms-2">Loading products...</span>
                    </div>

                    <!-- Products Container -->
                    <div id="productsContainer" class="row g-3 g-md-4">
                        <!-- Products will be dynamically loaded here -->
                    </div>

                    <!-- Pagination -->
                    <div id="paginationContainer" class="mt-5">
                        <!-- Pagination will be dynamically loaded here -->
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addProductModalLabel">
                        <i class="bi bi-plus-circle me-2"></i>Add New Product
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addProductForm" data-validate="true">
                        <div class="row g-3">
                            <div class="col-12 col-md-6">
                                <label for="addProductName" class="form-label">Product Name *</label>
                                <input type="text" class="form-control" id="addProductName" name="name" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label for="addProductSku" class="form-label">SKU *</label>
                                <input type="text" class="form-control" id="addProductSku" name="sku" required placeholder="e.g., PROD-001">
                            </div>
                            <div class="col-12">
                                <label for="addProductDescription" class="form-label">Description</label>
                                <textarea class="form-control" id="addProductDescription" name="description" rows="3"></textarea>
                            </div>
                            <div class="col-12 col-md-6">
                                <label for="addProductPrice" class="form-label">Price *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="addProductPrice" name="price" required min="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <label for="addProductCategory" class="form-label">Category *</label>
                                <select class="form-select" id="addProductCategory" name="category" required>
                                    <option value="">Select Category</option>
                                </select>
                            </div>
                            <div class="col-12 col-md-6">
                                <label for="addProductQuantity" class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="addProductQuantity" name="quantity" min="0" value="0">
                            </div>
                            <div class="col-12 col-md-6">
                                <label for="addProductImage" class="form-label">Image URL</label>
                                <input type="url" class="form-control" id="addProductImage" name="image" placeholder="https://example.com/image.jpg">
                            </div>
                            <div class="col-12">
                                <label for="addProductTags" class="form-label">Tags</label>
                                <input type="text" class="form-control" id="addProductTags" name="tags" placeholder="tag1, tag2, tag3">
                                <div class="form-text">Separate tags with commas</div>
                            </div>
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="addProductInStock" name="inStock" checked>
                                    <label class="form-check-label" for="addProductInStock">
                                        In Stock
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="addProductForm" class="btn btn-primary">
                        <i class="bi bi-check-circle me-1"></i>Add Product
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Product Modal -->
    <div class="modal fade" id="editProductModal" tabindex="-1" aria-labelledby="editProductModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editProductModalLabel">
                        <i class="bi bi-pencil me-2"></i>Edit Product
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editProductForm" data-validate="true">
                        <input type="hidden" name="id">
                        <div class="row g-3">
                            <div class="col-12 col-md-6">
                                <label for="editProductName" class="form-label">Product Name *</label>
                                <input type="text" class="form-control" id="editProductName" name="name" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label for="editProductSku" class="form-label">SKU *</label>
                                <input type="text" class="form-control" id="editProductSku" name="sku" required placeholder="e.g., PROD-001">
                            </div>
                            <div class="col-12">
                                <label for="editProductDescription" class="form-label">Description</label>
                                <textarea class="form-control" id="editProductDescription" name="description" rows="3"></textarea>
                            </div>
                            <div class="col-12 col-md-6">
                                <label for="editProductPrice" class="form-label">Price *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="editProductPrice" name="price" required min="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <label for="editProductCategory" class="form-label">Category *</label>
                                <select class="form-select" id="editProductCategory" name="category" required>
                                    <option value="">Select Category</option>
                                </select>
                            </div>
                            <div class="col-12 col-md-6">
                                <label for="editProductQuantity" class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="editProductQuantity" name="quantity" min="0" value="0">
                            </div>
                            <div class="col-12 col-md-6">
                                <label for="editProductImage" class="form-label">Image URL</label>
                                <input type="url" class="form-control" id="editProductImage" name="image" placeholder="https://example.com/image.jpg">
                            </div>
                            <div class="col-12">
                                <label for="editProductTags" class="form-label">Tags</label>
                                <input type="text" class="form-control" id="editProductTags" name="tags" placeholder="tag1, tag2, tag3">
                                <div class="form-text">Separate tags with commas</div>
                            </div>
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editProductInStock" name="inStock">
                                    <label class="form-check-label" for="editProductInStock">
                                        In Stock
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="editProductForm" class="btn btn-primary">
                        <i class="bi bi-check-circle me-1"></i>Update Product
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light border-top mt-5 py-4">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-12 col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-box-seam me-2"></i>
                        <span class="fw-bold">Model Master</span>
                        <span class="ms-2 text-muted">v1.0.0</span>
                    </div>
                    <small class="text-muted">Product Management System</small>
                </div>
                <div class="col-12 col-md-6 mt-3 mt-md-0">
                    <div class="d-flex justify-content-md-end align-items-center gap-3">
                        <small class="text-muted">
                            Built with Bootstrap & JavaScript
                        </small>
                        <div class="d-flex gap-2">
                            <a href="#" class="text-muted" title="Documentation">
                                <i class="bi bi-book"></i>
                            </a>
                            <a href="#" class="text-muted" title="GitHub">
                                <i class="bi bi-github"></i>
                            </a>
                            <a href="#" class="text-muted" title="Support">
                                <i class="bi bi-question-circle"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <!-- Model Master JavaScript -->
    <script src="Model.cs"></script>
    <script src="Model.js"></script>

    <!-- Initialize Application -->
    <script>
        // Application initialization is handled in Model.js
        // The DOMContentLoaded event listener will automatically initialize the app
    </script>
</body>
</html>