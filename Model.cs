/**
 * Model Master - Product Model and Data Layer
 * MVC Architecture - Model Layer
 * No hardcoded values - all configuration from Model.json
 */

class ModelMaster {
    constructor() {
        this.config = null;
        this.products = [];
        this.categories = [];
        this.init();
    }

    /**
     * Initialize the Model Master application
     */
    async init() {
        try {
            await this.loadConfig();
            this.loadSampleData();
            this.setupEventListeners();
        } catch (error) {
            console.error('Failed to initialize Model Master:', error);
        }
    }

    /**
     * Load configuration from Model.json
     */
    async loadConfig() {
        try {
            const response = await fetch('./Model.json');
            this.config = await response.json();
            this.categories = this.config.categories || [];
            this.products = this.config.sampleProducts || [];
        } catch (error) {
            console.error('Failed to load configuration:', error);
            throw error;
        }
    }

    /**
     * Load sample data into the application
     */
    loadSampleData() {
        if (this.config && this.config.sampleProducts) {
            this.products = [...this.config.sampleProducts];
        }
    }

    /**
     * Setup global event listeners
     */
    setupEventListeners() {
        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
        });
    }
}

/**
 * Product Model Class
 */
class Product {
    constructor(data = {}) {
        this.id = data.id || this.generateId();
        this.name = data.name || '';
        this.description = data.description || '';
        this.price = parseFloat(data.price) || 0;
        this.category = data.category || '';
        this.categoryId = data.categoryId || null;
        this.sku = data.sku || '';
        this.inStock = data.inStock !== undefined ? data.inStock : true;
        this.quantity = parseInt(data.quantity) || 0;
        this.image = data.image || '';
        this.tags = Array.isArray(data.tags) ? data.tags : [];
        this.rating = parseFloat(data.rating) || 0;
        this.reviews = parseInt(data.reviews) || 0;
        this.createdAt = data.createdAt || new Date().toISOString();
        this.updatedAt = data.updatedAt || new Date().toISOString();
    }

    /**
     * Generate unique ID for product
     */
    generateId() {
        return Date.now() + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Validate product data
     */
    validate(config) {
        const errors = [];
        const validation = config?.validation?.product;

        if (!validation) return errors;

        // Validate name
        if (validation.name?.required && !this.name.trim()) {
            errors.push('Product name is required');
        }
        if (this.name.length < validation.name?.minLength) {
            errors.push(`Product name must be at least ${validation.name.minLength} characters`);
        }
        if (this.name.length > validation.name?.maxLength) {
            errors.push(`Product name must not exceed ${validation.name.maxLength} characters`);
        }

        // Validate price
        if (validation.price?.required && (this.price === null || this.price === undefined)) {
            errors.push('Product price is required');
        }
        if (this.price < validation.price?.min) {
            errors.push(`Product price must be at least ${validation.price.min}`);
        }
        if (this.price > validation.price?.max) {
            errors.push(`Product price must not exceed ${validation.price.max}`);
        }

        // Validate category
        if (validation.category?.required && !this.category.trim()) {
            errors.push('Product category is required');
        }

        // Validate SKU
        if (validation.sku?.required && !this.sku.trim()) {
            errors.push('Product SKU is required');
        }
        if (validation.sku?.pattern && !new RegExp(validation.sku.pattern).test(this.sku)) {
            errors.push('Product SKU format is invalid');
        }

        return errors;
    }

    /**
     * Convert product to JSON
     */
    toJSON() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            price: this.price,
            category: this.category,
            categoryId: this.categoryId,
            sku: this.sku,
            inStock: this.inStock,
            quantity: this.quantity,
            image: this.image,
            tags: this.tags,
            rating: this.rating,
            reviews: this.reviews,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    /**
     * Update product data
     */
    update(data) {
        Object.keys(data).forEach(key => {
            if (key !== 'id' && key !== 'createdAt' && this.hasOwnProperty(key)) {
                this[key] = data[key];
            }
        });
        this.updatedAt = new Date().toISOString();
    }
}

/**
 * Data Service Class for managing products
 */
class DataService {
    constructor(modelMaster) {
        this.modelMaster = modelMaster;
        this.storageKey = 'modelMaster_products';
    }

    /**
     * Get all products
     */
    getAllProducts() {
        return this.modelMaster.products || [];
    }

    /**
     * Get product by ID
     */
    getProductById(id) {
        return this.modelMaster.products.find(product => product.id == id);
    }

    /**
     * Add new product
     */
    addProduct(productData) {
        const product = new Product(productData);
        const errors = product.validate(this.modelMaster.config);

        if (errors.length > 0) {
            throw new Error(errors.join(', '));
        }

        this.modelMaster.products.push(product);
        this.saveToStorage();
        return product;
    }

    /**
     * Update existing product
     */
    updateProduct(id, productData) {
        const productIndex = this.modelMaster.products.findIndex(p => p.id == id);
        if (productIndex === -1) {
            throw new Error('Product not found');
        }

        const product = new Product({ ...this.modelMaster.products[productIndex], ...productData });
        const errors = product.validate(this.modelMaster.config);

        if (errors.length > 0) {
            throw new Error(errors.join(', '));
        }

        this.modelMaster.products[productIndex] = product;
        this.saveToStorage();
        return product;
    }

    /**
     * Delete product
     */
    deleteProduct(id) {
        const productIndex = this.modelMaster.products.findIndex(p => p.id == id);
        if (productIndex === -1) {
            throw new Error('Product not found');
        }

        const deletedProduct = this.modelMaster.products.splice(productIndex, 1)[0];
        this.saveToStorage();
        return deletedProduct;
    }

    /**
     * Search products
     */
    searchProducts(query, filters = {}) {
        let results = this.getAllProducts();

        // Text search
        if (query && query.trim()) {
            const searchTerm = query.toLowerCase().trim();
            results = results.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm) ||
                product.category.toLowerCase().includes(searchTerm) ||
                product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
            );
        }

        // Category filter
        if (filters.category) {
            results = results.filter(product => product.category === filters.category);
        }

        // Price range filter
        if (filters.minPrice !== undefined) {
            results = results.filter(product => product.price >= filters.minPrice);
        }
        if (filters.maxPrice !== undefined) {
            results = results.filter(product => product.price <= filters.maxPrice);
        }

        // In stock filter
        if (filters.inStock !== undefined) {
            results = results.filter(product => product.inStock === filters.inStock);
        }

        return results;
    }

    /**
     * Get products with pagination
     */
    getProductsPaginated(page = 1, itemsPerPage = null) {
        const perPage = itemsPerPage || this.modelMaster.config?.config?.itemsPerPage || 12;
        const startIndex = (page - 1) * perPage;
        const endIndex = startIndex + perPage;

        const products = this.getAllProducts();
        const totalItems = products.length;
        const totalPages = Math.ceil(totalItems / perPage);

        return {
            products: products.slice(startIndex, endIndex),
            pagination: {
                currentPage: page,
                totalPages: totalPages,
                totalItems: totalItems,
                itemsPerPage: perPage,
                hasNext: page < totalPages,
                hasPrev: page > 1
            }
        };
    }

    /**
     * Save products to localStorage
     */
    saveToStorage() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.modelMaster.products));
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
        }
    }

    /**
     * Load products from localStorage
     */
    loadFromStorage() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const products = JSON.parse(stored);
                this.modelMaster.products = products.map(p => new Product(p));
            }
        } catch (error) {
            console.error('Failed to load from localStorage:', error);
        }
    }

    /**
     * Get categories
     */
    getCategories() {
        return this.modelMaster.categories || [];
    }

    /**
     * Get category by ID
     */
    getCategoryById(id) {
        return this.modelMaster.categories.find(cat => cat.id == id);
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ModelMaster, Product, DataService };
}