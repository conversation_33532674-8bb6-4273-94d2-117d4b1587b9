# Model Master - Product Management System

A modern, responsive product management system built with HTML, CSS, JavaScript, and Bootstrap. Features a complete MVC architecture with no hardcoded values, fully responsive design, and user-friendly interface.

## 🚀 Features

- **MVC Architecture**: Clean separation of Model, View, and Controller layers
- **Responsive Design**: Mobile-first approach using Bootstrap grid system
- **No Hardcoded Values**: All configuration stored in JSON files
- **Real-time Search**: Instant product search with debounced input
- **Advanced Filtering**: Filter by category, price range, and stock status
- **Multiple View Modes**: Grid and list view options
- **Form Validation**: Client-side validation with real-time feedback
- **Local Storage**: Persistent data storage in browser
- **Accessibility**: WCAG compliant with proper ARIA labels
- **Dark Mode Support**: Automatic dark mode based on system preference
- **Print Friendly**: Optimized styles for printing

## 📁 Project Structure

```
ModelMaster/
├── Model.html          # Main HTML structure and responsive layout
├── Model.css           # Custom CSS with CSS variables and responsive utilities
├── Model.js            # JavaScript controllers and business logic
├── Model.cs            # Model classes and data services (JavaScript)
├── Model.json          # Configuration and sample data
└── README.md           # This documentation file
```

## 🛠️ Technologies Used

- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Custom properties, Grid, Flexbox, and responsive design
- **JavaScript ES6+**: Modern JavaScript with classes and modules
- **Bootstrap 5.3.2**: Responsive framework and components
- **Bootstrap Icons**: Icon library for UI elements
- **Google Fonts**: Inter font family for typography

## 🎯 Getting Started

### Prerequisites

- Modern web browser (Chrome, Firefox, Safari, Edge)
- Local web server (optional but recommended)

### Installation

1. **Clone or download** the project files to your local machine

2. **Open the project** in your preferred code editor

3. **Serve the files** using a local web server:
   ```bash
   # Using Python 3
   python -m http.server 8000
   
   # Using Node.js (http-server)
   npx http-server
   
   # Using PHP
   php -S localhost:8000
   ```

4. **Open your browser** and navigate to `http://localhost:8000`

### Direct File Access

You can also open `Model.html` directly in your browser, but some features may be limited due to CORS restrictions.

## 📖 Usage Guide

### Adding Products

1. Click the **"Add Product"** button in the header
2. Fill in the required fields (Name, SKU, Price, Category)
3. Optionally add description, quantity, image URL, and tags
4. Click **"Add Product"** to save

### Editing Products

1. Click the **"Edit"** button on any product card
2. Modify the desired fields in the modal
3. Click **"Update Product"** to save changes

### Searching and Filtering

- **Search**: Type in the search box to find products by name, description, or tags
- **Category Filter**: Select a category from the dropdown
- **Price Range**: Set minimum and maximum price values
- **Stock Filter**: Toggle to show only in-stock items
- **Clear Filters**: Reset all filters with one click

### View Options

- **Grid View**: Default card-based layout
- **List View**: Horizontal layout with more details
- **Sorting**: Sort by name, price, rating, or date

## ⚙️ Configuration

### Model.json Structure

The `Model.json` file contains all configuration and sample data:

```json
{
  "app": {
    "name": "Model Master",
    "version": "1.0.0",
    "description": "Product Model Management System"
  },
  "config": {
    "itemsPerPage": 12,
    "maxFileSize": 5242880,
    "currency": "USD"
  },
  "ui": {
    "theme": {
      "primaryColor": "var(--bs-primary)"
    },
    "gridColumns": {
      "xs": 12, "sm": 6, "md": 4, "lg": 3, "xl": 2
    }
  },
  "validation": {
    "product": {
      "name": { "required": true, "minLength": 2, "maxLength": 100 },
      "price": { "required": true, "min": 0, "max": 999999.99 }
    }
  }
}
```

### Customization

1. **Colors**: Modify CSS custom properties in `Model.css`
2. **Layout**: Adjust Bootstrap grid columns in `Model.json`
3. **Validation**: Update validation rules in `Model.json`
4. **Categories**: Add/remove categories in `Model.json`

## 🎨 Responsive Design

The application uses Bootstrap's responsive grid system:

- **xs (0px+)**: 1 column (mobile)
- **sm (576px+)**: 2 columns (large mobile)
- **md (768px+)**: 3 columns (tablet)
- **lg (992px+)**: 4 columns (desktop)
- **xl (1200px+)**: 6 columns (large desktop)
- **xxl (1400px+)**: 6 columns (extra large)

## 🔧 API Reference

### ProductController Methods

- `loadProducts()`: Load and display products with pagination
- `handleSearch(query)`: Filter products by search query
- `handleAddProduct(formData)`: Add new product
- `handleEditProduct(formData)`: Update existing product
- `handleDeleteProduct(id)`: Remove product
- `setView(mode)`: Switch between grid/list view

### DataService Methods

- `getAllProducts()`: Get all products
- `getProductById(id)`: Get specific product
- `searchProducts(query, filters)`: Search with filters
- `addProduct(data)`: Create new product
- `updateProduct(id, data)`: Update product
- `deleteProduct(id)`: Remove product

## 🎯 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📱 Mobile Features

- Touch-friendly interface
- Swipe gestures (where applicable)
- Responsive typography
- Optimized button sizes
- Collapsible navigation

## 🔒 Security Considerations

- Input validation on all forms
- XSS prevention through proper escaping
- CSRF protection (implement server-side)
- Content Security Policy headers (recommended)

## 🚀 Performance Optimizations

- Lazy loading for images
- Debounced search input
- Efficient DOM manipulation
- CSS animations with GPU acceleration
- Minimal external dependencies

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the code comments

## 🔄 Version History

- **v1.0.0**: Initial release with full MVC architecture
  - Product CRUD operations
  - Responsive design
  - Search and filtering
  - Local storage persistence

---

**Built with ❤️ using modern web technologies**
